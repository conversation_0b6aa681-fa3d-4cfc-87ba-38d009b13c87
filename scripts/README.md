# Graphiti Utility Scripts

This directory contains utility scripts for working with Graphiti knowledge graphs. Scripts are organized by functionality into subdirectories.

## Directory Structure

```
scripts/
├── conversation_loaders/    # Scripts for loading conversation data
├── visualization/          # Graph visualization utilities
├── setup/                 # Setup and configuration scripts
└── integrations/          # Third-party service integrations
```

## Available Scripts

### Conversation Loaders (`conversation_loaders/`)

Scripts for importing conversation data from various sources into Graphiti.

- **`conversation_loader.py`** - Full-featured ChatGPT conversation loader with batch processing
- **`simple_conversation_loader.py`** - Simplified version for basic use cases
- **`test_parser.py`** - Test script to validate conversation JSON parsing
- **`requirements_loader.txt`** - Python dependencies for conversation loaders
- **`setup_conversation_loader.sh`** - Automated setup script

**Usage:**
```bash
# Set up environment
cd scripts/conversation_loaders
./setup_conversation_loader.sh

# Test parsing
python3 test_parser.py

# Load conversations
python3 conversation_loader.py
```

### Visualization (`visualization/`)

Tools for visualizing and exploring Graphiti knowledge graphs.

- **`visualize_graph.py`** - Graph visualization and statistics script

**Usage:**
```bash
python3 scripts/visualization/visualize_graph.py
```

### Setup (`setup/`)

Configuration and setup utilities for Graphiti and related services.

- **`bloom_setup.py`** - Neo4j Bloom configuration and perspective setup

**Usage:**
```bash
python3 scripts/setup/bloom_setup.py
```

### Integrations (`integrations/`)

Scripts for integrating Graphiti with external services and platforms.

- **`zep_integration.py`** - Integration with Zep memory service

**Usage:**
```bash
# Set ZEP_API_KEY environment variable first
export ZEP_API_KEY="your_api_key"
python3 scripts/integrations/zep_integration.py
```

## General Requirements

Most scripts require:
- Python 3.10+
- Neo4j database running
- Environment variables configured (see `.env.example`)

## Environment Setup

1. **Copy environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Configure required variables:**
   ```bash
   NEO4J_URI=bolt://localhost:7687
   NEO4J_USER=neo4j
   NEO4J_PASSWORD=your_password
   OPENAI_API_KEY=your_openai_key
   ```

3. **Install dependencies:**
   ```bash
   # For conversation loaders
   pip install -r scripts/conversation_loaders/requirements_loader.txt
   
   # For main Graphiti functionality
   pip install -e .
   ```

## Running Scripts

All scripts should be run from the repository root directory:

```bash
# Correct
python3 scripts/conversation_loaders/conversation_loader.py

# Incorrect (may cause import issues)
cd scripts/conversation_loaders
python3 conversation_loader.py
```

## Contributing

When adding new scripts:

1. Place them in the appropriate subdirectory
2. Add documentation to this README
3. Include any specific requirements or dependencies
4. Follow the existing naming conventions
5. Add error handling and logging
6. Test with sample data

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're running scripts from the repository root
2. **Connection Errors**: Verify Neo4j is running and credentials are correct
3. **API Key Errors**: Check that required environment variables are set
4. **File Not Found**: Verify data files are in the correct locations under `data/`

### Getting Help

- Check individual script documentation
- Review the main project README
- Check the `docs/` directory for additional documentation
- Open an issue on the project repository
