# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Repository reorganization with improved directory structure
- Comprehensive documentation for scripts and configuration
- Environment configuration template (`.env.example`)
- README files for `scripts/` and `config/` directories
- Security improvements for API key handling

### Changed
- **BREAKING**: Moved utility scripts to organized subdirectories under `scripts/`
  - `conversation_loader.py` → `scripts/conversation_loaders/conversation_loader.py`
  - `simple_conversation_loader.py` → `scripts/conversation_loaders/simple_conversation_loader.py`
  - `test_parser.py` → `scripts/conversation_loaders/test_parser.py`
  - `visualize_graph.py` → `scripts/visualization/visualize_graph.py`
  - `bloom_setup.py` → `scripts/setup/bloom_setup.py`
  - `zep_integration.py` → `scripts/integrations/zep_integration.py`
- **BREAKING**: Moved configuration files to `config/` directory
  - Docker Compose files moved to `config/docker/`
- **BREAKING**: Moved sample data to `data/` directory
  - `conversations_sample.json` → `data/samples/conversations_sample.json`
  - Configuration files → `data/configs/`
- Updated all documentation to reflect new file locations
- Improved security by removing hardcoded API keys

### Fixed
- Security vulnerability: Removed hardcoded API key from Zep integration script
- Updated file references in documentation and scripts
- Fixed import paths and file references after reorganization

### Security
- **CRITICAL**: Sanitized hardcoded API key in Zep integration script
- Added proper environment variable handling for sensitive credentials
- Created comprehensive `.env.example` template for secure configuration

## [0.17.4] - Previous Release

### Added
- Initial repository structure
- Core Graphiti functionality
- Basic utility scripts
- Docker configuration
- Documentation

---

## Migration Guide for Repository Reorganization

If you have existing scripts or configurations that reference the old file locations, please update them as follows:

### Script Locations
```bash
# Old locations → New locations
conversation_loader.py → scripts/conversation_loaders/conversation_loader.py
simple_conversation_loader.py → scripts/conversation_loaders/simple_conversation_loader.py
test_parser.py → scripts/conversation_loaders/test_parser.py
visualize_graph.py → scripts/visualization/visualize_graph.py
bloom_setup.py → scripts/setup/bloom_setup.py
zep_integration.py → scripts/integrations/zep_integration.py
```

### Configuration Files
```bash
# Old locations → New locations
docker-compose.yml → config/docker/docker-compose.yml
docker-compose.test.yml → config/docker/docker-compose.test.yml
docker-compose.bloom.yml → config/docker/docker-compose.bloom.yml
```

### Data Files
```bash
# Old locations → New locations
conversations_sample.json → data/samples/conversations_sample.json
bloom_config_all.json → data/configs/bloom_config_all.json
depot.json → data/configs/depot.json
```

### Running Scripts
All scripts should now be run from the repository root:
```bash
# Correct way to run scripts
python3 scripts/conversation_loaders/conversation_loader.py
python3 scripts/visualization/visualize_graph.py
python3 scripts/setup/bloom_setup.py

# Update any existing automation or CI/CD pipelines accordingly
```

### Environment Variables
Create a `.env` file based on the new `.env.example` template:
```bash
cp .env.example .env
# Edit .env with your actual values
```

### Docker Usage
Update Docker Compose commands:
```bash
# Old
docker-compose up -d

# New
docker-compose -f config/docker/docker-compose.yml up -d
```

---

## Notes

- This changelog will be maintained going forward for all releases
- Breaking changes are clearly marked and include migration instructions
- Security fixes are prioritized and clearly documented
- All changes follow semantic versioning principles
