#!/usr/bin/env python3
"""
Simple graph visualization script for Graphiti conversation data.
"""

import json
from neo4j import GraphDatabase
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

def connect_to_neo4j():
    """Connect to Neo4j database."""
    uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    user = os.getenv('NEO4J_USER', 'neo4j')
    password = os.getenv('NEO4J_PASSWORD', 'password')
    
    driver = GraphDatabase.driver(uri, auth=(user, password))
    return driver

def get_graph_data(driver, group_id='chatgpt_conversations', limit=50):
    """Get comprehensive graph data from Neo4j."""
    with driver.session() as session:
        # Get entities with embeddings and creation info
        entities_query = """
        MATCH (n:Entity)
        WHERE n.group_id = $group_id
        OPTIONAL MATCH (n)-[r:RELATES_TO]-()
        RETURN n.uuid as id, n.name as name, n.summary as summary,
               labels(n) as labels, n.created_at as created_at,
               count(r) as relationship_count
        ORDER BY n.created_at DESC
        LIMIT $limit
        """

        # Get relationships with temporal info
        relationships_query = """
        MATCH (n:Entity)-[r:RELATES_TO]->(m:Entity)
        WHERE n.group_id = $group_id
        RETURN n.uuid as source, m.uuid as target, r.fact as relationship,
               r.uuid as id, r.created_at as created_at, r.valid_at as valid_at,
               r.invalid_at as invalid_at
        ORDER BY r.created_at DESC
        LIMIT $limit
        """

        # Get episodes with mentions
        episodes_query = """
        MATCH (e:Episodic)
        WHERE e.group_id = $group_id
        OPTIONAL MATCH (e)-[:MENTIONS]->(entity:Entity)
        RETURN e.uuid as id, e.name as name,
               substring(e.content, 0, 200) as content,
               e.created_at as created, e.source as source,
               collect(entity.name) as mentioned_entities
        ORDER BY e.created_at DESC
        LIMIT $limit
        """

        # Get communities if they exist
        communities_query = """
        MATCH (c:Community)
        WHERE c.group_id = $group_id
        OPTIONAL MATCH (c)<-[:HAS_MEMBER]-(e:Entity)
        RETURN c.uuid as id, c.name as name, c.summary as summary,
               collect(e.name) as members
        LIMIT $limit
        """

        # Get entity mention relationships
        mentions_query = """
        MATCH (e:Episodic)-[m:MENTIONS]->(entity:Entity)
        WHERE e.group_id = $group_id
        RETURN e.uuid as episode_id, entity.uuid as entity_id,
               e.name as episode_name, entity.name as entity_name
        LIMIT $limit
        """

        # Get graph statistics
        stats_query = """
        MATCH (n) WHERE n.group_id = $group_id
        WITH labels(n) as nodeLabels
        UNWIND nodeLabels as label
        RETURN label, count(*) as count
        ORDER BY count DESC
        """

        # Get temporal patterns
        temporal_query = """
        MATCH (e:Episodic) WHERE e.group_id = $group_id
        RETURN date(e.created_at) as date, count(*) as episode_count
        ORDER BY date DESC
        LIMIT 30
        """

        # Get most connected entities
        central_entities_query = """
        MATCH (n:Entity) WHERE n.group_id = $group_id
        OPTIONAL MATCH (n)-[r:RELATES_TO]-()
        OPTIONAL MATCH (n)<-[m:MENTIONS]-()
        RETURN n.name, n.uuid,
               count(r) as connections,
               count(m) as mentions
        ORDER BY connections DESC, mentions DESC
        LIMIT 10
        """

        entities = list(session.run(entities_query, group_id=group_id, limit=limit))
        relationships = list(session.run(relationships_query, group_id=group_id, limit=limit))
        episodes = list(session.run(episodes_query, group_id=group_id, limit=limit))
        communities = list(session.run(communities_query, group_id=group_id, limit=limit))
        mentions = list(session.run(mentions_query, group_id=group_id, limit=limit))
        stats = list(session.run(stats_query, group_id=group_id))
        temporal = list(session.run(temporal_query, group_id=group_id))
        central_entities = list(session.run(central_entities_query, group_id=group_id))

        return {
            'entities': entities,
            'relationships': relationships,
            'episodes': episodes,
            'communities': communities,
            'mentions': mentions,
            'stats': stats,
            'temporal': temporal,
            'central_entities': central_entities
        }

def print_graph_summary(graph_data):
    """Print a comprehensive summary of the graph data."""
    entities = graph_data['entities']
    relationships = graph_data['relationships']
    episodes = graph_data['episodes']
    communities = graph_data['communities']
    mentions = graph_data['mentions']
    stats = graph_data['stats']
    temporal = graph_data['temporal']
    central_entities = graph_data['central_entities']

    print("=" * 60)
    print("🧠 GRAPHITI CONVERSATION GRAPH SUMMARY")
    print("=" * 60)

    print(f"\n📊 COMPREHENSIVE STATISTICS:")
    print(f"   • Entities: {len(entities)}")
    print(f"   • Relationships: {len(relationships)}")
    print(f"   • Episodes (Messages): {len(episodes)}")
    print(f"   • Communities: {len(communities)}")
    print(f"   • Entity Mentions: {len(mentions)}")

    # Show node type distribution
    if stats:
        print(f"\n📈 NODE TYPE DISTRIBUTION:")
        for stat in stats:
            print(f"   • {stat['label']}: {stat['count']}")

    # Show most connected entities
    if central_entities:
        print(f"\n� MOST CONNECTED ENTITIES:")
        for entity in central_entities[:5]:
            print(f"   • {entity['name']}: {entity['connections']} relationships, {entity['mentions']} mentions")

    # Show temporal activity
    if temporal:
        print(f"\n📅 RECENT ACTIVITY (Episodes by Date):")
        for temp in temporal[:5]:
            print(f"   • {temp['date']}: {temp['episode_count']} episodes")

    print(f"\n🏷️  RECENT ENTITIES:")
    for entity in entities[:10]:  # Show first 10
        name = entity['name']
        summary = entity['summary'][:100] + "..." if len(entity['summary']) > 100 else entity['summary']
        rel_count = entity.get('relationship_count', 0)
        print(f"   • {name} ({rel_count} relationships): {summary}")

    if len(entities) > 10:
        print(f"   ... and {len(entities) - 10} more entities")

    print(f"\n🔗 RECENT RELATIONSHIPS:")
    for rel in relationships[:10]:  # Show first 10
        valid_info = ""
        if rel.get('valid_at'):
            valid_info = f" (valid from {rel['valid_at']})"
        if rel.get('invalid_at'):
            valid_info += f" (invalid from {rel['invalid_at']})"
        print(f"   • {rel['relationship']}{valid_info}")

    if len(relationships) > 10:
        print(f"   ... and {len(relationships) - 10} more relationships")

    # Show communities if they exist
    if communities:
        print(f"\n🏘️  COMMUNITIES:")
        for community in communities[:5]:
            members = community.get('members', [])
            member_count = len([m for m in members if m])  # Filter out None values
            print(f"   • {community['name']}: {member_count} members")
            if community.get('summary'):
                summary = community['summary'][:100] + "..." if len(community['summary']) > 100 else community['summary']
                print(f"     {summary}")

    print(f"\n💬 RECENT CONVERSATION EPISODES:")
    for episode in episodes[:5]:  # Show first 5
        name = episode['name']
        content_preview = episode['content'][:80] + "..." if len(episode['content']) > 80 else episode['content']
        mentioned = episode.get('mentioned_entities', [])
        mentioned_count = len([m for m in mentioned if m])  # Filter out None values
        print(f"   • {name} (mentions {mentioned_count} entities)")
        print(f"     {content_preview}")
        if mentioned_count > 0:
            mentioned_names = [m for m in mentioned if m][:3]  # Show first 3
            print(f"     Mentions: {', '.join(mentioned_names)}")
        print()

def generate_mermaid_diagram(graph_data):
    """Generate a Mermaid diagram representation."""
    entities = graph_data['entities']
    relationships = graph_data['relationships']
    communities = graph_data['communities']

    print("\n" + "=" * 60)
    print("🎨 ENHANCED MERMAID DIAGRAM (Copy to https://mermaid.live)")
    print("=" * 60)

    mermaid = ["graph TD"]

    # Add entity nodes
    entity_map = {}
    for i, entity in enumerate(entities[:15]):  # Limit to 15 for readability
        node_id = f"E{i}"
        entity_map[entity['id']] = node_id
        name = entity['name'].replace('"', "'")
        rel_count = entity.get('relationship_count', 0)
        mermaid.append(f'    {node_id}["{name}<br/>({rel_count} rels)"]')

    # Add community nodes if they exist
    community_map = {}
    for i, community in enumerate(communities[:5]):  # Limit communities
        node_id = f"C{i}"
        community_map[community['id']] = node_id
        name = community['name'].replace('"', "'")
        members = community.get('members', [])
        member_count = len([m for m in members if m])
        mermaid.append(f'    {node_id}{{"{name}<br/>({member_count} members)"}}')

    # Add entity relationships
    for rel in relationships[:20]:  # Limit relationships
        source_id = entity_map.get(rel['source'])
        target_id = entity_map.get(rel['target'])
        if source_id and target_id:
            fact = rel['relationship'][:30] + "..." if len(rel['relationship']) > 30 else rel['relationship']
            fact = fact.replace('"', "'")
            # Show temporal info if available
            if rel.get('valid_at'):
                mermaid.append(f'    {source_id} -->|"{fact}"| {target_id}')
            else:
                mermaid.append(f'    {source_id} --> {target_id}')

    # Style different node types
    mermaid.append("    classDef entity fill:#e1f5fe,stroke:#01579b,stroke-width:2px")
    mermaid.append("    classDef community fill:#f3e5f5,stroke:#4a148c,stroke-width:3px")

    # Apply styles
    for node_id in entity_map.values():
        mermaid.append(f"    class {node_id} entity")
    for node_id in community_map.values():
        mermaid.append(f"    class {node_id} community")

    print("\n".join(mermaid))
    print("\n" + "=" * 60)


def print_bloom_setup_instructions():
    """Print instructions for setting up Neo4j Bloom."""
    print("\n" + "=" * 60)
    print("🌸 NEO4J BLOOM SETUP INSTRUCTIONS")
    print("=" * 60)

    print(f"\n📋 QUICK START:")
    print(f"   1. Run the Bloom setup script:")
    print(f"      python scripts/setup/bloom_setup.py")
    print(f"   2. Open Neo4j Desktop or Browser")
    print(f"   3. Connect to your database (bolt://localhost:7687)")
    print(f"   4. Access Neo4j Bloom")
    print(f"   5. Import perspectives from ./bloom_perspectives/")

    print(f"\n🎯 AVAILABLE PERSPECTIVES:")
    print(f"   • Entity Relationships - Visualize entity connections")
    print(f"   • Episode Timeline - Temporal view of conversations")
    print(f"   • Community Analysis - Entity clustering and communities")

    print(f"\n🚀 DOCKER OPTIONS:")
    print(f"   • Basic setup: docker-compose up")
    print(f"   • Bloom-optimized: docker-compose -f docker-compose.bloom.yml up")

    print(f"\n📖 DOCUMENTATION:")
    print(f"   • Full guide: docs/neo4j-bloom-integration.md")
    print(f"   • Perspective docs: bloom_perspectives/README.md")

    print(f"\n💡 TIPS:")
    print(f"   • Start with small datasets (LIMIT 50)")
    print(f"   • Use group_id filters for better performance")
    print(f"   • Create custom perspectives for your specific use cases")
    print("=" * 60)

def main():
    """Main function."""
    try:
        driver = connect_to_neo4j()
        graph_data = get_graph_data(driver)

        print_graph_summary(graph_data)
        generate_mermaid_diagram(graph_data)
        print_bloom_setup_instructions()
        
        print(f"\n🌐 NEO4J ACCESS INFORMATION:")
        print(f"   • Neo4j Browser: http://localhost:7474")
        print(f"   • Connection URI: bolt://localhost:7687")
        print(f"   • Username: neo4j")
        print(f"   • Password: password")

        print(f"\n🌸 NEO4J BLOOM INTEGRATION:")
        print(f"   • Access Bloom through Neo4j Desktop or Browser")
        print(f"   • Pre-configured perspectives available in: ./bloom_perspectives/")
        print(f"   • Run setup script: python scripts/setup/bloom_setup.py")
        print(f"   • Documentation: docs/neo4j-bloom-integration.md")

        print(f"\n🔍 ENHANCED SAMPLE QUERIES FOR NEO4J BROWSER:")
        print(f"   1. View entities with relationship counts:")
        print(f"      MATCH (n:Entity) WHERE n.group_id = 'chatgpt_conversations' ")
        print(f"      OPTIONAL MATCH (n)-[r:RELATES_TO]-() ")
        print(f"      RETURN n.name, n.summary, count(r) as connections LIMIT 20")
        print(f"   2. View relationships with temporal info:")
        print(f"      MATCH (n:Entity)-[r:RELATES_TO]->(m:Entity) WHERE n.group_id = 'chatgpt_conversations' ")
        print(f"      RETURN n.name, r.fact, m.name, r.valid_at, r.invalid_at LIMIT 15")
        print(f"   3. View episodes with mentioned entities:")
        print(f"      MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity) WHERE e.group_id = 'chatgpt_conversations' ")
        print(f"      RETURN e.name, collect(entity.name) as mentions ORDER BY e.created_at DESC LIMIT 10")
        print(f"   4. View communities and their members:")
        print(f"      MATCH (c:Community)<-[:HAS_MEMBER]-(e:Entity) WHERE c.group_id = 'chatgpt_conversations' ")
        print(f"      RETURN c.name, c.summary, collect(e.name) as members LIMIT 10")
        print(f"   5. Find most connected entities:")
        print(f"      MATCH (n:Entity) WHERE n.group_id = 'chatgpt_conversations' ")
        print(f"      OPTIONAL MATCH (n)-[r:RELATES_TO]-() OPTIONAL MATCH (n)<-[m:MENTIONS]-() ")
        print(f"      RETURN n.name, count(r) as connections, count(m) as mentions ")
        print(f"      ORDER BY connections DESC, mentions DESC LIMIT 10")

        print(f"\n🎨 ENHANCED BLOOM-OPTIMIZED QUERIES:")
        print(f"   1. Entity network with temporal relationships:")
        print(f"      MATCH (n:Entity)-[r:RELATES_TO]-(m:Entity) WHERE n.group_id = 'chatgpt_conversations' ")
        print(f"      RETURN n, r, m ORDER BY r.created_at DESC LIMIT 50")
        print(f"   2. Episode timeline with entity mentions:")
        print(f"      MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity) WHERE e.group_id = 'chatgpt_conversations' ")
        print(f"      RETURN e, entity ORDER BY e.created_at DESC LIMIT 30")
        print(f"   3. Community structure with member details:")
        print(f"      MATCH (c:Community)<-[:HAS_MEMBER]-(e:Entity) WHERE c.group_id = 'chatgpt_conversations' ")
        print(f"      RETURN c, e LIMIT 100")
        print(f"   4. Temporal activity patterns:")
        print(f"      MATCH (e:Episodic) WHERE e.group_id = 'chatgpt_conversations' ")
        print(f"      RETURN date(e.created_at) as date, count(*) as episodes ORDER BY date DESC LIMIT 30")
        print(f"   5. Entity influence network:")
        print(f"      MATCH (n:Entity) WHERE n.group_id = 'chatgpt_conversations' ")
        print(f"      OPTIONAL MATCH (n)-[r:RELATES_TO]-() OPTIONAL MATCH (n)<-[m:MENTIONS]-() ")
        print(f"      WITH n, count(r) as connections, count(m) as mentions ")
        print(f"      WHERE connections > 2 OR mentions > 1 RETURN n ORDER BY connections DESC, mentions DESC LIMIT 25")
        
        driver.close()
        
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure Neo4j is running: docker-compose ps")
        print("2. Check Neo4j logs: docker logs graphiti-neo4j-1")
        print("3. Verify credentials in .env file")

if __name__ == "__main__":
    main()
