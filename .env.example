# Graphiti Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# Neo4j Database Configuration
# =============================================================================

# Neo4j connection URI (bolt protocol)
NEO4J_URI=bolt://localhost:7687

# Neo4j authentication
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Alternative Neo4j URIs for different environments
# NEO4J_URI=neo4j://localhost:7687          # For Neo4j 4.0+
# NEO4J_URI=bolt+s://your-aura-instance.databases.neo4j.io:7687  # For Neo4j Aura

# =============================================================================
# OpenAI Configuration (Required for LLM functionality)
# =============================================================================

# OpenAI API key for LLM and embedding services
OPENAI_API_KEY=sk-your-openai-api-key-here

# Optional: OpenAI organization ID
# OPENAI_ORG_ID=org-your-organization-id

# Optional: OpenAI model configuration
# OPENAI_MODEL=gpt-4
# OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# =============================================================================
# Alternative LLM Providers (Optional)
# =============================================================================

# Anthropic Claude
# ANTHROPIC_API_KEY=your-anthropic-api-key

# Google Gemini
# GOOGLE_API_KEY=your-google-api-key

# Groq
# GROQ_API_KEY=your-groq-api-key

# =============================================================================
# Embedding Providers (Optional)
# =============================================================================

# Voyage AI
# VOYAGE_API_KEY=your-voyage-api-key

# Azure OpenAI (if using Azure instead of OpenAI)
# AZURE_OPENAI_API_KEY=your-azure-openai-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_API_VERSION=2023-05-15

# =============================================================================
# Third-party Integrations
# =============================================================================

# Zep Memory Service
# ZEP_API_KEY=your-zep-api-key

# =============================================================================
# Conversation Loader Configuration
# =============================================================================

# Path to ChatGPT conversations JSON file
CONVERSATIONS_FILE=data/samples/conversations_sample.json

# Group ID for organizing conversations in Graphiti
GROUP_ID=chatgpt_conversations

# Batch processing settings
BATCH_SIZE=10
START_FROM=0

# =============================================================================
# Development and Testing
# =============================================================================

# Environment mode
# ENVIRONMENT=development
# ENVIRONMENT=production
# ENVIRONMENT=testing

# Logging level
# LOG_LEVEL=INFO
# LOG_LEVEL=DEBUG
# LOG_LEVEL=WARNING

# Enable debug mode for detailed logging
# DEBUG=true

# =============================================================================
# Docker Configuration (if using Docker Compose)
# =============================================================================

# Neo4j Docker settings
# NEO4J_AUTH=neo4j/password
# NEO4J_PLUGINS=["apoc"]

# =============================================================================
# Performance and Scaling
# =============================================================================

# Neo4j memory settings (for Docker)
# NEO4J_dbms_memory_heap_initial__size=512m
# NEO4J_dbms_memory_heap_max__size=2G
# NEO4J_dbms_memory_pagecache_size=1G

# Connection pool settings
# NEO4J_MAX_CONNECTION_POOL_SIZE=100
# NEO4J_CONNECTION_TIMEOUT=30

# =============================================================================
# Security Settings
# =============================================================================

# Enable/disable telemetry
# TELEMETRY_ENABLED=false

# API rate limiting
# RATE_LIMIT_ENABLED=true
# RATE_LIMIT_REQUESTS_PER_MINUTE=60

# =============================================================================
# Backup and Maintenance
# =============================================================================

# Backup directory
# BACKUP_DIR=/path/to/backups

# Maintenance schedule
# MAINTENANCE_ENABLED=true
# MAINTENANCE_SCHEDULE=0 2 * * *  # Daily at 2 AM

# =============================================================================
# Notes
# =============================================================================

# 1. Never commit the actual .env file to version control
# 2. Keep your API keys secure and rotate them regularly
# 3. Use different configurations for development, testing, and production
# 4. Some settings may require restarting services to take effect
# 5. Refer to the documentation for detailed configuration options
