#!/usr/bin/env python3
"""
Neo4j Bloom Setup and Configuration Script for Graphiti

This script helps users set up Neo4j Bloom perspectives and provides
sample queries optimized for Graphiti's data model.
"""

import json
import os
from typing import Dict, List, Any
from neo4j import GraphDatabase
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class BloomConfigurator:
    """Configure Neo4j Bloom for Graphiti data visualization."""
    
    def __init__(self):
        """Initialize the Bloom configurator."""
        self.uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        self.user = os.getenv('NEO4J_USER', 'neo4j')
        self.password = os.getenv('NEO4J_PASSWORD', 'password')
        self.driver = None
        
    def connect(self):
        """Connect to Neo4j database."""
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
            # Test connection
            with self.driver.session() as session:
                session.run("RETURN 1")
            print(f"✅ Connected to Neo4j at {self.uri}")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Neo4j: {e}")
            return False
    
    def close(self):
        """Close database connection."""
        if self.driver:
            self.driver.close()
    
    def analyze_graph_structure(self, group_id: str = None) -> Dict[str, Any]:
        """Analyze the current graph structure for Bloom configuration."""
        with self.driver.session() as session:
            # Count nodes by type
            node_counts = {}
            for label in ['Entity', 'Episodic', 'Community']:
                query = f"MATCH (n:{label})"
                if group_id:
                    query += f" WHERE n.group_id = '{group_id}'"
                query += " RETURN count(n) as count"
                
                result = session.run(query)
                count = result.single()['count']
                node_counts[label] = count
            
            # Count relationships
            rel_counts = {}
            for rel_type in ['RELATES_TO', 'MENTIONS', 'BELONGS_TO']:
                query = f"MATCH ()-[r:{rel_type}]->()"
                if group_id:
                    query += f" WHERE EXISTS((startNode(r).group_id)) AND startNode(r).group_id = '{group_id}'"
                query += " RETURN count(r) as count"
                
                result = session.run(query)
                count = result.single()['count']
                rel_counts[rel_type] = count
            
            # Get sample properties
            sample_properties = {}
            if node_counts['Entity'] > 0:
                query = "MATCH (n:Entity)"
                if group_id:
                    query += f" WHERE n.group_id = '{group_id}'"
                query += " RETURN keys(n) as props LIMIT 1"
                
                result = session.run(query)
                record = result.single()
                if record:
                    sample_properties['Entity'] = record['props']
            
            return {
                'node_counts': node_counts,
                'relationship_counts': rel_counts,
                'sample_properties': sample_properties,
                'total_nodes': sum(node_counts.values()),
                'total_relationships': sum(rel_counts.values())
            }
    
    def create_bloom_perspectives(self) -> List[Dict[str, Any]]:
        """Create Bloom perspective configurations for Graphiti data."""
        
        perspectives = [
            {
                "name": "Entity Relationships",
                "description": "Visualize how entities relate to each other",
                "node_categories": [
                    {
                        "label": "Entity",
                        "color": "#1f77b4",  # Blue
                        "size_property": "degree",
                        "caption_properties": ["name"],
                        "search_properties": ["name", "summary"]
                    },
                    {
                        "label": "Community", 
                        "color": "#2ca02c",  # Green
                        "size": "large",
                        "caption_properties": ["name"],
                        "search_properties": ["name", "summary"]
                    }
                ],
                "relationship_categories": [
                    {
                        "type": "RELATES_TO",
                        "color": "#666666",
                        "caption_properties": ["fact"],
                        "thickness_property": "weight"
                    }
                ],
                "sample_queries": [
                    "MATCH (n:Entity) WHERE n.group_id = $group_id RETURN n LIMIT 50",
                    "MATCH (n:Entity)-[r:RELATES_TO]-(m:Entity) WHERE n.group_id = $group_id RETURN n, r, m LIMIT 30"
                ]
            },
            {
                "name": "Episode Timeline",
                "description": "Show temporal flow of episodes and entity mentions",
                "node_categories": [
                    {
                        "label": "Episodic",
                        "color": "#ff7f0e",  # Orange
                        "size_property": "content_length",
                        "caption_properties": ["name"],
                        "search_properties": ["name", "content"]
                    },
                    {
                        "label": "Entity",
                        "color": "#1f77b4",  # Blue
                        "size": "small",
                        "caption_properties": ["name"],
                        "search_properties": ["name"]
                    }
                ],
                "relationship_categories": [
                    {
                        "type": "MENTIONS",
                        "color": "#999999",
                        "caption_properties": ["created_at"]
                    }
                ],
                "layout": "timeline",
                "timeline_property": "created_at",
                "sample_queries": [
                    "MATCH (e:Episodic) WHERE e.group_id = $group_id RETURN e ORDER BY e.created_at DESC LIMIT 20",
                    "MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity) WHERE e.group_id = $group_id RETURN e, entity ORDER BY e.created_at DESC LIMIT 30"
                ]
            },
            {
                "name": "Community Analysis", 
                "description": "Analyze entity communities and clusters",
                "node_categories": [
                    {
                        "label": "Community",
                        "color": "#2ca02c",  # Green
                        "size": "large",
                        "caption_properties": ["name"],
                        "search_properties": ["name", "summary"]
                    },
                    {
                        "label": "Entity",
                        "color_property": "community_id",
                        "size": "medium",
                        "caption_properties": ["name"],
                        "search_properties": ["name"]
                    }
                ],
                "relationship_categories": [
                    {
                        "type": "BELONGS_TO",
                        "color": "#2ca02c"
                    },
                    {
                        "type": "RELATES_TO",
                        "color": "#666666",
                        "caption_properties": ["fact"]
                    }
                ],
                "clustering": "group_id",
                "sample_queries": [
                    "MATCH (c:Community) WHERE c.group_id = $group_id RETURN c LIMIT 20",
                    "MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity) WHERE c.group_id = $group_id RETURN c, e LIMIT 50"
                ]
            }
        ]
        
        return perspectives
    
    def generate_sample_queries(self, group_id: str = None) -> Dict[str, List[str]]:
        """Generate sample queries for Bloom exploration."""
        
        group_filter = f" WHERE n.group_id = '{group_id}'" if group_id else ""
        group_filter_rel = f" WHERE startNode(r).group_id = '{group_id}'" if group_id else ""
        
        queries = {
            "Basic Exploration": [
                f"MATCH (n:Entity){group_filter} RETURN n LIMIT 50",
                f"MATCH (e:Episodic){group_filter} RETURN e ORDER BY e.created_at DESC LIMIT 20",
                f"MATCH (n:Entity)-[r:RELATES_TO]-(m:Entity){group_filter_rel} RETURN n, r, m LIMIT 30"
            ],
            "Temporal Analysis": [
                f"MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity) WHERE e.group_id = '{group_id}' AND e.created_at > datetime() - duration('P7D') RETURN e, entity ORDER BY e.created_at DESC",
                f"MATCH (n:Entity)-[r:RELATES_TO]->(m:Entity) WHERE n.group_id = '{group_id}' AND r.created_at IS NOT NULL RETURN n, r, m ORDER BY r.created_at DESC LIMIT 30"
            ],
            "Network Analysis": [
                f"MATCH (n:Entity){group_filter} WITH n, size((n)-[:RELATES_TO]-()) as degree WHERE degree > 3 RETURN n ORDER BY degree DESC LIMIT 20",
                f"MATCH path = (start:Entity)-[:RELATES_TO*2..3]-(end:Entity) WHERE start.group_id = '{group_id}' RETURN path LIMIT 10"
            ],
            "Community Discovery": [
                f"MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity) WHERE c.group_id = '{group_id}' RETURN c, e LIMIT 100",
                f"MATCH (e1:Entity)-[r:RELATES_TO]->(e2:Entity) WHERE e1.group_id = '{group_id}' AND e1.community_id <> e2.community_id RETURN e1, r, e2"
            ]
        }
        
        return queries
    
    def export_bloom_config(self, group_id: str = None, output_file: str = "bloom_config.json"):
        """Export complete Bloom configuration to JSON file."""
        
        config = {
            "connection": {
                "uri": self.uri,
                "username": self.user,
                "database": "neo4j"
            },
            "graph_analysis": self.analyze_graph_structure(group_id),
            "perspectives": self.create_bloom_perspectives(),
            "sample_queries": self.generate_sample_queries(group_id),
            "recommended_settings": {
                "max_nodes": 500,
                "max_relationships": 1000,
                "layout_algorithm": "force-directed",
                "enable_physics": True,
                "node_size_range": [10, 50],
                "relationship_thickness_range": [1, 5]
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(config, f, indent=2, default=str)
        
        print(f"✅ Bloom configuration exported to {output_file}")
        return config


def main():
    """Main function to set up Bloom configuration."""
    print("🌸 Neo4j Bloom Setup for Graphiti")
    print("=" * 50)
    
    configurator = BloomConfigurator()
    
    if not configurator.connect():
        print("\n❌ Cannot proceed without database connection.")
        print("Please ensure Neo4j is running and credentials are correct.")
        return
    
    try:
        # Get group_id from user
        group_id = input("\nEnter group_id to analyze (or press Enter for all data): ").strip()
        if not group_id:
            group_id = None
        
        # Analyze current graph
        print(f"\n📊 Analyzing graph structure...")
        analysis = configurator.analyze_graph_structure(group_id)
        
        print(f"\n📈 Graph Statistics:")
        print(f"   • Total Nodes: {analysis['total_nodes']}")
        print(f"   • Total Relationships: {analysis['total_relationships']}")
        print(f"   • Entities: {analysis['node_counts']['Entity']}")
        print(f"   • Episodes: {analysis['node_counts']['Episodic']}")
        print(f"   • Communities: {analysis['node_counts']['Community']}")
        
        # Generate configuration
        print(f"\n🎨 Generating Bloom perspectives...")
        perspectives = configurator.create_bloom_perspectives()
        print(f"   • Created {len(perspectives)} perspectives")
        
        # Export configuration
        output_file = f"bloom_config_{group_id or 'all'}.json"
        config = configurator.export_bloom_config(group_id, output_file)
        
        print(f"\n🌐 Neo4j Bloom Access Information:")
        print(f"   • Neo4j Browser: http://localhost:7474")
        print(f"   • Connection URI: {configurator.uri}")
        print(f"   • Username: {configurator.user}")
        print(f"   • Password: {configurator.password}")
        
        print(f"\n📋 Next Steps:")
        print(f"   1. Open Neo4j Desktop or Browser")
        print(f"   2. Connect to your database")
        print(f"   3. Open Neo4j Bloom")
        print(f"   4. Import the generated perspective configurations")
        print(f"   5. Start exploring your Graphiti knowledge graph!")
        
        print(f"\n📖 For detailed instructions, see: docs/neo4j-bloom-integration.md")
        
    except Exception as e:
        print(f"❌ Error during setup: {e}")
    finally:
        configurator.close()


if __name__ == "__main__":
    main()
