{"title": "Graphiti Knowledge Graph Dashboard", "version": "2.4", "settings": {"pagenumber": 0, "editable": true, "fullscreenEnabled": false, "parameters": {}}, "pages": [{"title": "Overview", "reports": [{"title": "Node Count <PERSON>", "query": "MATCH (n) RETURN labels(n)[0] as NodeType, count(n) as Count ORDER BY Count DESC", "width": 4, "height": 3, "x": 0, "y": 0, "type": "bar", "selection": {}, "settings": {"nodeColorScheme": "category10"}}, {"title": "Relationship Types", "query": "MATCH ()-[r]->() RETURN type(r) as RelationshipType, count(r) as Count ORDER BY Count DESC", "width": 4, "height": 3, "x": 4, "y": 0, "type": "bar", "selection": {}, "settings": {"nodeColorScheme": "category10"}}, {"title": "Recent Entities", "query": "MATCH (e:Entity) RETURN e.name as Name, e.summary as Summary, e.created_at as Created ORDER BY e.created_at DESC LIMIT 10", "width": 4, "height": 3, "x": 8, "y": 0, "type": "table", "selection": {}, "settings": {}}, {"title": "Entity Network", "query": "MATCH (e1:Entity)-[r:RELATES_TO]-(e2:Entity) RETURN e1, r, e2 LIMIT 50", "width": 12, "height": 6, "x": 0, "y": 3, "type": "graph", "selection": {}, "settings": {"nodeColorScheme": "category10", "defaultNodeSize": "large", "nodeColorProp": "name", "nodeLabelProp": "name", "relationshipThickness": "count"}}]}, {"title": "Entities", "reports": [{"title": "Entity Distribution by Group", "query": "MATCH (e:Entity) WHERE e.group_id IS NOT NULL RETURN e.group_id as GroupID, count(e) as EntityCount ORDER BY EntityCount DESC", "width": 6, "height": 4, "x": 0, "y": 0, "type": "pie", "selection": {}, "settings": {"nodeColorScheme": "category10"}}, {"title": "Entity Timeline", "query": "MATCH (e:Entity) WHERE e.created_at IS NOT NULL RETURN date(e.created_at) as Date, count(e) as EntitiesCreated ORDER BY Date", "width": 6, "height": 4, "x": 6, "y": 0, "type": "line", "selection": {}, "settings": {"nodeColorScheme": "category10"}}, {"title": "Top Connected Entities", "query": "MATCH (e:Entity)-[r:RELATES_TO]-() RETURN e.name as EntityName, e.summary as Summary, count(r) as ConnectionCount ORDER BY ConnectionCount DESC LIMIT 15", "width": 12, "height": 4, "x": 0, "y": 4, "type": "table", "selection": {}, "settings": {}}]}, {"title": "Episodes", "reports": [{"title": "Episode Timeline", "query": "MATCH (ep:Episodic) WHERE ep.created_at IS NOT NULL RETURN date(ep.created_at) as Date, count(ep) as EpisodesCreated ORDER BY Date", "width": 8, "height": 4, "x": 0, "y": 0, "type": "line", "selection": {}, "settings": {"nodeColorScheme": "category10"}}, {"title": "Episodes by Group", "query": "MATCH (ep:Episodic) WHERE ep.group_id IS NOT NULL RETURN ep.group_id as GroupID, count(ep) as EpisodeCount ORDER BY EpisodeCount DESC", "width": 4, "height": 4, "x": 8, "y": 0, "type": "pie", "selection": {}, "settings": {"nodeColorScheme": "category10"}}, {"title": "Episode-Entity Connections", "query": "MATCH (ep:Episodic)-[m:MENTIONS]->(e:Entity) RETURN ep.name as Episode, e.name as <PERSON>tity, ep.created_at as EpisodeDate ORDER BY EpisodeDate DESC LIMIT 20", "width": 12, "height": 4, "x": 0, "y": 4, "type": "table", "selection": {}, "settings": {}}]}, {"title": "Relationships", "reports": [{"title": "Relationship Facts Word Cloud", "query": "MATCH ()-[r:RELATES_TO]->() WHERE r.fact IS NOT NULL RETURN r.fact as Fact LIMIT 100", "width": 6, "height": 6, "x": 0, "y": 0, "type": "value", "selection": {}, "settings": {}}, {"title": "Relationship Network by Strength", "query": "MATCH (e1:Entity)-[r:RELATES_TO]-(e2:Entity) WHERE r.fact IS NOT NULL RETURN e1, r, e2 ORDER BY r.created_at DESC LIMIT 30", "width": 6, "height": 6, "x": 6, "y": 0, "type": "graph", "selection": {}, "settings": {"nodeColorScheme": "category10", "defaultNodeSize": "large", "nodeColorProp": "name", "nodeLabelProp": "name", "relationshipThickness": "count"}}]}]}