#!/usr/bin/env python3
"""
Simplified ChatGPT Conversation Loader for Graphiti.

This script loads ChatGPT conversation data from a JSON export file into a Graphiti knowledge graph.
Based on the quickstart example pattern.
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timezone
from typing import Dict, List, Any

from dotenv import load_dotenv
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()


def parse_conversations(json_file_path: str) -> List[Dict[str, Any]]:
    """Parse the conversations JSON file."""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        conversations = json.load(f)
    
    logger.info(f"Loaded {len(conversations)} conversations from {json_file_path}")
    return conversations


def extract_conversation_messages(conversation: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract messages from a conversation in chronological order."""
    mapping = conversation.get('mapping', {})
    messages = []
    
    # Find all messages with actual content
    for node_id, node_data in mapping.items():
        message = node_data.get('message')
        if not message:
            continue
            
        # Skip system messages and empty messages
        author = message.get('author', {})
        role = author.get('role', '')
        content = message.get('content', {})
        parts = content.get('parts', [])
        
        if role == 'system' or not parts or not parts[0]:
            continue
        
        # Extract message text
        message_text = parts[0] if parts else ""
        
        # Handle case where message_text might be a dict (e.g., tool responses)
        if isinstance(message_text, dict):
            # Skip tool messages or convert to string representation
            continue
        
        if not message_text or not str(message_text).strip():
            continue
        
        create_time = message.get('create_time')
        if create_time:
            timestamp = datetime.fromtimestamp(create_time, tz=timezone.utc)
        else:
            # Use conversation create_time as fallback
            timestamp = datetime.fromtimestamp(
                conversation.get('create_time', 0), 
                tz=timezone.utc
            )
        
        messages.append({
            'id': message.get('id', node_id),
            'role': role,
            'content': message_text,
            'timestamp': timestamp,
            'parent': node_data.get('parent'),
            'children': node_data.get('children', [])
        })
    
    # Sort messages by timestamp
    messages.sort(key=lambda x: x['timestamp'])
    return messages


async def load_conversation(graphiti: Graphiti, conversation: Dict[str, Any], group_id: str = "conversations") -> None:
    """Load a single conversation into Graphiti."""
    title = conversation.get('title', 'Untitled Conversation')
    conversation_id = conversation.get('id', conversation.get('conversation_id', 'unknown'))
    
    logger.info(f"Loading conversation: {title}")
    
    # Extract messages from the conversation
    messages = extract_conversation_messages(conversation)
    
    if not messages:
        logger.warning(f"  No messages found in conversation: {title}")
        return
    
    logger.info(f"  Found {len(messages)} messages")
    
    # Load each message as an episode
    for i, message in enumerate(messages):
        episode_name = f"{title} - Message {i+1}"
        
        # Format the message content with role information
        role = message['role']
        content = message['content']
        
        if role == 'user':
            episode_body = f"User: {content}"
        elif role == 'assistant':
            episode_body = f"Assistant: {content}"
        else:
            episode_body = f"{role.title()}: {content}"
        
        try:
            await graphiti.add_episode(
                name=episode_name,
                episode_body=episode_body,
                source=EpisodeType.message,
                source_description=f"ChatGPT conversation: {title}",
                reference_time=message['timestamp'],
                group_id=group_id
            )
            logger.info(f"    Added message {i+1}/{len(messages)}: {role}")
            
        except Exception as e:
            logger.error(f"    Error adding message {i+1}: {e}")
            continue
    
    logger.info(f"  Completed loading conversation: {title}")


async def main():
    """Main function to run the conversation loader."""
    # Configuration
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'password')
    
    # Path to your conversations JSON file
    conversations_file = os.getenv('CONVERSATIONS_FILE', '../../data/samples/conversations_sample.json')
    
    # Group ID for organizing the conversations
    group_id = os.getenv('GROUP_ID', 'chatgpt_conversations')
    
    # Batch processing settings
    batch_size = int(os.getenv('BATCH_SIZE', '1'))  # Process 1 conversation at a time for testing
    start_from = int(os.getenv('START_FROM', '0'))   # Start from beginning (0-indexed)
    
    # Verify the file exists
    if not os.path.exists(conversations_file):
        logger.error(f"File '{conversations_file}' not found!")
        logger.error("Please make sure the file path is correct.")
        return
    
    logger.info(f"Configuration:")
    logger.info(f"  📁 File: {conversations_file}")
    logger.info(f"  🏷️  Group ID: {group_id}")
    logger.info(f"  📦 Batch size: {batch_size}")
    logger.info(f"  ▶️  Starting from: {start_from}")
    logger.info(f"  🔗 Neo4j URI: {neo4j_uri}")
    
    # Initialize Graphiti with Neo4j connection
    graphiti = Graphiti(neo4j_uri, neo4j_user, neo4j_password)
    
    try:
        logger.info("Starting conversation loading process...")
        
        # Initialize Graphiti indices and constraints
        await graphiti.build_indices_and_constraints()
        logger.info("Graphiti indices and constraints built")
        
        # Parse conversations
        conversations = parse_conversations(conversations_file)
        
        # Apply start_from filter
        if start_from > 0:
            conversations = conversations[start_from:]
            logger.info(f"Starting from conversation {start_from + 1}")
        
        # Process conversations in batches
        total_conversations = len(conversations)
        processed = 0
        
        for i in range(0, min(total_conversations, batch_size)):  # Only process batch_size conversations
            conversation = conversations[i]
            conv_num = start_from + i + 1
            logger.info(f"\nProcessing conversation {conv_num}/{start_from + total_conversations}")
            try:
                await load_conversation(graphiti, conversation, group_id)
                processed += 1
            except Exception as e:
                title = conversation.get('title', 'Unknown')
                logger.error(f"Error loading conversation '{title}': {e}")
                continue
        
        logger.info(f"\n🎉 Completed loading {processed} conversations into Graphiti!")
        
    except Exception as e:
        logger.error(f"Error during loading: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Close the connection
        await graphiti.close()
        logger.info("Connection closed")


if __name__ == "__main__":
    asyncio.run(main())
