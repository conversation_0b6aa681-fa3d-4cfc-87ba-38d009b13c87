#!/bin/bash

# Setup script for the conversation loader

echo "🚀 Setting up ChatGPT Conversation Loader for Graphiti"
echo "=================================================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip first."
    exit 1
fi

echo "✅ pip3 found"

# Install required packages
echo "📦 Installing required packages..."
pip3 install -r requirements_loader.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install packages. Please check the error above."
    exit 1
fi

echo "✅ Packages installed successfully"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  No .env file found. Creating one from .env.example..."
    cp .env.example .env
    echo "📝 Please edit the .env file and add your:"
    echo "   - NEO4J_URI (e.g., bolt://localhost:7687)"
    echo "   - NEO4J_USER (e.g., neo4j)"
    echo "   - NEO4J_PASSWORD (your Neo4j password)"
    echo "   - OPENAI_API_KEY (your OpenAI API key)"
    echo ""
    echo "💡 You can edit it with: nano .env"
else
    echo "✅ .env file already exists"
fi

# Check if conversations file exists
if [ ! -f "../../data/samples/conversations_sample.json" ]; then
    echo "⚠️  Conversations file 'data/samples/conversations_sample.json' not found."
    echo "   Please make sure your ChatGPT conversations JSON file is in the data/samples/ directory."
    echo "   You can rename it or update the CONVERSATIONS_FILE path in conversation_loader.py"
else
    echo "✅ Conversations file found"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Make sure Neo4j is running"
echo "2. Edit .env file with your credentials: nano .env"
echo "3. Test the parser: python3 scripts/conversation_loaders/test_parser.py"
echo "4. Run the loader: python3 scripts/conversation_loaders/conversation_loader.py"
echo ""
echo "📚 For more information, see README_conversation_loader.md"
