#!/usr/bin/env python3
"""
Simple graph visualization script for Graphiti conversation data.
"""

import json
from neo4j import GraphDatabase
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

def connect_to_neo4j():
    """Connect to Neo4j database."""
    uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    user = os.getenv('NEO4J_USER', 'neo4j')
    password = os.getenv('NEO4J_PASSWORD', 'password')
    
    driver = GraphDatabase.driver(uri, auth=(user, password))
    return driver

def get_graph_data(driver):
    """Get graph data from Neo4j."""
    with driver.session() as session:
        # Get entities
        entities_query = """
        MATCH (n:Entity) 
        WHERE n.group_id = 'chatgpt_conversations'
        RETURN n.uuid as id, n.name as name, n.summary as summary, labels(n) as labels
        LIMIT 20
        """
        
        # Get relationships
        relationships_query = """
        MATCH (n:Entity)-[r:RELATES_TO]->(m:Entity) 
        WHERE n.group_id = 'chatgpt_conversations'
        RETURN n.uuid as source, m.uuid as target, r.fact as relationship, r.uuid as id
        LIMIT 30
        """
        
        # Get episodes
        episodes_query = """
        MATCH (e:Episodic) 
        WHERE e.group_id = 'chatgpt_conversations'
        RETURN e.uuid as id, e.name as name, substring(e.content, 0, 200) as content, e.created_at as created
        ORDER BY e.created_at
        """
        
        entities = list(session.run(entities_query))
        relationships = list(session.run(relationships_query))
        episodes = list(session.run(episodes_query))
        
        return entities, relationships, episodes

def print_graph_summary(entities, relationships, episodes):
    """Print a summary of the graph data."""
    print("=" * 60)
    print("🧠 GRAPHITI CONVERSATION GRAPH SUMMARY")
    print("=" * 60)
    
    print(f"\n📊 STATISTICS:")
    print(f"   • Entities: {len(entities)}")
    print(f"   • Relationships: {len(relationships)}")
    print(f"   • Episodes (Messages): {len(episodes)}")
    
    print(f"\n🏷️  ENTITIES EXTRACTED:")
    for entity in entities[:10]:  # Show first 10
        name = entity['name']
        summary = entity['summary'][:100] + "..." if len(entity['summary']) > 100 else entity['summary']
        print(f"   • {name}: {summary}")
    
    if len(entities) > 10:
        print(f"   ... and {len(entities) - 10} more entities")
    
    print(f"\n🔗 KEY RELATIONSHIPS:")
    for rel in relationships[:10]:  # Show first 10
        print(f"   • {rel['relationship']}")
    
    if len(relationships) > 10:
        print(f"   ... and {len(relationships) - 10} more relationships")
    
    print(f"\n💬 CONVERSATION EPISODES:")
    for episode in episodes:
        name = episode['name']
        content_preview = episode['content'][:80] + "..." if len(episode['content']) > 80 else episode['content']
        print(f"   • {name}")
        print(f"     {content_preview}")
        print()

def generate_mermaid_diagram(entities, relationships):
    """Generate a Mermaid diagram representation."""
    print("\n" + "=" * 60)
    print("🎨 MERMAID DIAGRAM (Copy to https://mermaid.live)")
    print("=" * 60)
    
    mermaid = ["graph TD"]
    
    # Add nodes
    entity_map = {}
    for i, entity in enumerate(entities[:15]):  # Limit to 15 for readability
        node_id = f"E{i}"
        entity_map[entity['id']] = node_id
        name = entity['name'].replace('"', "'")
        mermaid.append(f'    {node_id}["{name}"]')
    
    # Add relationships
    for rel in relationships[:20]:  # Limit relationships
        source_id = entity_map.get(rel['source'])
        target_id = entity_map.get(rel['target'])
        if source_id and target_id:
            fact = rel['relationship'][:30] + "..." if len(rel['relationship']) > 30 else rel['relationship']
            fact = fact.replace('"', "'")
            mermaid.append(f'    {source_id} --> {target_id}')
    
    print("\n".join(mermaid))
    print("\n" + "=" * 60)


def print_bloom_setup_instructions():
    """Print instructions for setting up Neo4j Bloom."""
    print("\n" + "=" * 60)
    print("🌸 NEO4J BLOOM SETUP INSTRUCTIONS")
    print("=" * 60)

    print(f"\n📋 QUICK START:")
    print(f"   1. Run the Bloom setup script:")
    print(f"      python scripts/setup/bloom_setup.py")
    print(f"   2. Open Neo4j Desktop or Browser")
    print(f"   3. Connect to your database (bolt://localhost:7687)")
    print(f"   4. Access Neo4j Bloom")
    print(f"   5. Import perspectives from ./bloom_perspectives/")

    print(f"\n🎯 AVAILABLE PERSPECTIVES:")
    print(f"   • Entity Relationships - Visualize entity connections")
    print(f"   • Episode Timeline - Temporal view of conversations")
    print(f"   • Community Analysis - Entity clustering and communities")

    print(f"\n🚀 DOCKER OPTIONS:")
    print(f"   • Basic setup: docker-compose up")
    print(f"   • Bloom-optimized: docker-compose -f docker-compose.bloom.yml up")

    print(f"\n📖 DOCUMENTATION:")
    print(f"   • Full guide: docs/neo4j-bloom-integration.md")
    print(f"   • Perspective docs: bloom_perspectives/README.md")

    print(f"\n💡 TIPS:")
    print(f"   • Start with small datasets (LIMIT 50)")
    print(f"   • Use group_id filters for better performance")
    print(f"   • Create custom perspectives for your specific use cases")
    print("=" * 60)

def main():
    """Main function."""
    try:
        driver = connect_to_neo4j()
        entities, relationships, episodes = get_graph_data(driver)
        
        print_graph_summary(entities, relationships, episodes)
        generate_mermaid_diagram(entities, relationships)
        print_bloom_setup_instructions()
        
        print(f"\n🌐 NEO4J ACCESS INFORMATION:")
        print(f"   • Neo4j Browser: http://localhost:7474")
        print(f"   • Connection URI: bolt://localhost:7687")
        print(f"   • Username: neo4j")
        print(f"   • Password: password")

        print(f"\n🌸 NEO4J BLOOM INTEGRATION:")
        print(f"   • Access Bloom through Neo4j Desktop or Browser")
        print(f"   • Pre-configured perspectives available in: ./bloom_perspectives/")
        print(f"   • Run setup script: python scripts/setup/bloom_setup.py")
        print(f"   • Documentation: docs/neo4j-bloom-integration.md")

        print(f"\n🔍 SAMPLE QUERIES FOR NEO4J BROWSER:")
        print(f"   1. View all entities:")
        print(f"      MATCH (n:Entity) WHERE n.group_id = 'chatgpt_conversations' RETURN n LIMIT 20")
        print(f"   2. View relationships:")
        print(f"      MATCH (n:Entity)-[r:RELATES_TO]->(m:Entity) WHERE n.group_id = 'chatgpt_conversations' RETURN n, r, m LIMIT 15")
        print(f"   3. View episodes:")
        print(f"      MATCH (e:Episodic) WHERE e.group_id = 'chatgpt_conversations' RETURN e ORDER BY e.created_at")

        print(f"\n🎨 BLOOM-OPTIMIZED QUERIES:")
        print(f"   1. Entity network (for Entity Relationships perspective):")
        print(f"      MATCH (n:Entity)-[r:RELATES_TO]-(m:Entity) WHERE n.group_id = 'chatgpt_conversations' RETURN n, r, m LIMIT 50")
        print(f"   2. Episode timeline (for Episode Timeline perspective):")
        print(f"      MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity) WHERE e.group_id = 'chatgpt_conversations' RETURN e, entity ORDER BY e.created_at DESC LIMIT 30")
        print(f"   3. Community structure (for Community Analysis perspective):")
        print(f"      MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity) WHERE c.group_id = 'chatgpt_conversations' RETURN c, e LIMIT 100")
        
        driver.close()
        
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure Neo4j is running: docker-compose ps")
        print("2. Check Neo4j logs: docker logs graphiti-neo4j-1")
        print("3. Verify credentials in .env file")

if __name__ == "__main__":
    main()
