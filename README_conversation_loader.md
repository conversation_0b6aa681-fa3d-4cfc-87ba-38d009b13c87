# ChatGPT Conversation Loader for Graphiti

This script loads ChatGPT conversation data from a JSON export file into a Graphiti knowledge graph.

## Features

- Parses ChatGPT conversation JSON exports
- Extracts individual messages from conversations
- Loads each message as an episode in Graphiti
- Preserves conversation structure and metadata
- <PERSON>les user and assistant messages
- Maintains chronological order
- Groups conversations by configurable group ID

## Prerequisites

1. **Neo4j Database**: You need a running Neo4j instance
2. **OpenAI API Key**: Required for Graphiti's LLM and embedding functionality
3. **Python 3.8+**: The script requires Python 3.8 or higher

## Installation

1. Install the required dependencies:
```bash
pip install -r scripts/conversation_loaders/requirements_loader.txt
```

2. Set up environment variables:
```bash
export NEO4J_URI="bolt://localhost:7687"
export NEO4J_USER="neo4j"
export NEO4J_PASSWORD="your_password"
export OPENAI_API_KEY="your_openai_api_key"
```

Or create a `.env` file:
```
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
OPENAI_API_KEY=your_openai_api_key
```

## Usage

1. **Prepare your conversation file**: Make sure your ChatGPT conversations JSON file is in the same directory as the script, or update the `CONVERSATIONS_FILE` path in the script.

2. **Run the loader**:
```bash
python scripts/conversation_loaders/conversation_loader.py
```

## Configuration

You can modify these settings in the `main()` function:

- `CONVERSATIONS_FILE`: Path to your conversations JSON file
- `GROUP_ID`: Group identifier for organizing conversations in Graphiti
- Database connection settings (if not using environment variables)

## How it Works

1. **Parsing**: The script reads the JSON file and extracts conversation metadata and message mappings
2. **Message Extraction**: For each conversation, it extracts individual messages, filtering out system messages and empty content
3. **Chronological Ordering**: Messages are sorted by timestamp to maintain conversation flow
4. **Episode Creation**: Each message becomes an episode in Graphiti with:
   - Name: "Conversation Title - Message N"
   - Content: "Role: Message content" (e.g., "User: What is ADHD?")
   - Source: Message type
   - Source Description: Conversation title
   - Reference Time: Original message timestamp
   - Group ID: Configurable group identifier

## Data Structure

Each conversation message is loaded as a Graphiti episode with:
- **Name**: Descriptive name including conversation title and message number
- **Episode Body**: Formatted message content with role prefix
- **Source**: `EpisodeType.message`
- **Source Description**: Original conversation title
- **Reference Time**: Original message timestamp
- **Group ID**: Configurable group for organization

## Example Output

```
Starting conversation loading process...
Graphiti indices and constraints built
Loaded 2 conversations from data/samples/conversations_sample.json

Processing conversation 1/2
Loading conversation: Understanding ADHD Symptoms
  Found 6 messages
    Added message 1/6: user
    Added message 2/6: assistant
    Added message 3/6: user
    Added message 4/6: assistant
    Added message 5/6: user
    Added message 6/6: assistant
  Completed loading conversation: Understanding ADHD Symptoms

Processing conversation 2/2
Loading conversation: Low-Abuse Stimulants for ADHD
  Found 12 messages
    Added message 1/12: user
    Added message 2/12: assistant
    ...
  Completed loading conversation: Low-Abuse Stimulants for ADHD

Completed loading 2 conversations into Graphiti!
```

## Troubleshooting

1. **Connection Issues**: Verify Neo4j is running and connection details are correct
2. **OpenAI API Issues**: Ensure your API key is valid and has sufficient credits
3. **File Not Found**: Check the path to your conversations JSON file
4. **Memory Issues**: For very large conversation files, consider processing in batches

## Customization

You can extend the script to:
- Add custom metadata extraction
- Filter conversations by date or topic
- Implement batch processing for large files
- Add conversation-level episodes in addition to message-level episodes
- Customize the episode naming scheme
- Add relationship extraction between conversations

## Notes

- The script skips system messages and empty content
- Messages are processed in chronological order
- Each conversation is processed as a separate group of episodes
- Error handling ensures that failed messages don't stop the entire process
