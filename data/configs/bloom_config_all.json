{"connection": {"uri": "bolt://localhost:7687", "username": "neo4j", "database": "neo4j"}, "graph_analysis": {"node_counts": {"Entity": 21, "Episodic": 10, "Community": 0}, "relationship_counts": {"RELATES_TO": 42, "MENTIONS": 37, "BELONGS_TO": 0}, "sample_properties": {"Entity": ["uuid", "name", "created_at", "group_id", "summary", "labels", "name_embedding"]}, "total_nodes": 31, "total_relationships": 79}, "perspectives": [{"name": "Entity Relationships", "description": "Visualize how entities relate to each other", "node_categories": [{"label": "Entity", "color": "#1f77b4", "size_property": "degree", "caption_properties": ["name"], "search_properties": ["name", "summary"]}, {"label": "Community", "color": "#2ca02c", "size": "large", "caption_properties": ["name"], "search_properties": ["name", "summary"]}], "relationship_categories": [{"type": "RELATES_TO", "color": "#666666", "caption_properties": ["fact"], "thickness_property": "weight"}], "sample_queries": ["MATCH (n:Entity) WHERE n.group_id = $group_id RETURN n LIMIT 50", "MATCH (n:Entity)-[r:RELATES_TO]-(m:Entity) WHERE n.group_id = $group_id RETURN n, r, m LIMIT 30"]}, {"name": "Episode Timeline", "description": "Show temporal flow of episodes and entity mentions", "node_categories": [{"label": "Episodic", "color": "#ff7f0e", "size_property": "content_length", "caption_properties": ["name"], "search_properties": ["name", "content"]}, {"label": "Entity", "color": "#1f77b4", "size": "small", "caption_properties": ["name"], "search_properties": ["name"]}], "relationship_categories": [{"type": "MENTIONS", "color": "#999999", "caption_properties": ["created_at"]}], "layout": "timeline", "timeline_property": "created_at", "sample_queries": ["MATCH (e:Episodic) WHERE e.group_id = $group_id RETURN e ORDER BY e.created_at DESC LIMIT 20", "MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity) WHERE e.group_id = $group_id RETURN e, entity ORDER BY e.created_at DESC LIMIT 30"]}, {"name": "Community Analysis", "description": "Analyze entity communities and clusters", "node_categories": [{"label": "Community", "color": "#2ca02c", "size": "large", "caption_properties": ["name"], "search_properties": ["name", "summary"]}, {"label": "Entity", "color_property": "community_id", "size": "medium", "caption_properties": ["name"], "search_properties": ["name"]}], "relationship_categories": [{"type": "BELONGS_TO", "color": "#2ca02c"}, {"type": "RELATES_TO", "color": "#666666", "caption_properties": ["fact"]}], "clustering": "group_id", "sample_queries": ["MATCH (c:Community) WHERE c.group_id = $group_id RETURN c LIMIT 20", "MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity) WHERE c.group_id = $group_id RETURN c, e LIMIT 50"]}], "sample_queries": {"Basic Exploration": ["MATCH (n:Entity) RETURN n LIMIT 50", "MATCH (e:Episodic) RETURN e ORDER BY e.created_at DESC LIMIT 20", "MATCH (n:Entity)-[r:RELATES_TO]-(m:Entity) RETURN n, r, m LIMIT 30"], "Temporal Analysis": ["MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity) WHERE e.group_id = 'None' AND e.created_at > datetime() - duration('P7D') RETURN e, entity ORDER BY e.created_at DESC", "MATCH (n:Entity)-[r:RELATES_TO]->(m:Entity) WHERE n.group_id = 'None' AND r.created_at IS NOT NULL RETURN n, r, m ORDER BY r.created_at DESC LIMIT 30"], "Network Analysis": ["MATCH (n:Entity) WITH n, size((n)-[:RELATES_TO]-()) as degree WHERE degree > 3 RETURN n ORDER BY degree DESC LIMIT 20", "MATCH path = (start:Entity)-[:RELATES_TO*2..3]-(end:Entity) WHERE start.group_id = 'None' RETURN path LIMIT 10"], "Community Discovery": ["MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity) WHERE c.group_id = 'None' RETURN c, e LIMIT 100", "MATCH (e1:Entity)-[r:RELATES_TO]->(e2:Entity) WHERE e1.group_id = 'None' AND e1.community_id <> e2.community_id RETURN e1, r, e2"]}, "recommended_settings": {"max_nodes": 500, "max_relationships": 1000, "layout_algorithm": "force-directed", "enable_physics": true, "node_size_range": [10, 50], "relationship_thickness_range": [1, 5]}}