# NeoDash Setup for Graphiti

NeoDash is a powerful dashboard application for Neo4j that allows you to create interactive visualizations and reports from your Graphiti knowledge graph data.

## Quick Start

### 1. Access NeoDash
NeoDash is now available at: **https://neodash.graphapp.io/**

### 2. Connect to Your Neo4j Database

When you open NeoDash, you'll need to connect to your local Neo4j instance:

- **Connection URL**: `bolt://localhost:7687`
- **Username**: `neo4j`
- **Password**: `password` (or your configured password)
- **Database**: `neo4j`

### 3. Import the Graphiti Dashboard

1. Click on the "Load Dashboard" button in NeoDash
2. Select "Import from JSON"
3. Upload the `graphiti-neodash-dashboard.json` file from this directory
4. The dashboard will load with pre-configured reports for your Graphiti data

## Dashboard Features

The pre-configured dashboard includes 4 pages:

### Overview Page
- **Node Count Summary**: Bar chart showing distribution of Entity and Episodic nodes
- **Relationship Types**: Bar chart of relationship type counts
- **Recent Entities**: Table of recently created entities
- **Entity Network**: Interactive graph visualization of entity relationships

### Entities Page
- **Entity Distribution by Group**: Pie chart showing entities by group_id
- **Entity Timeline**: Line chart of entity creation over time
- **Top Connected Entities**: Table of most connected entities

### Episodes Page
- **Episode Timeline**: Line chart of episode creation over time
- **Episodes by Group**: Pie chart of episodes by group_id
- **Episode-Entity Connections**: Table showing which episodes mention which entities

### Relationships Page
- **Relationship Facts**: Display of relationship facts
- **Relationship Network**: Graph showing entity relationships with facts

## Customizing Your Dashboard

### Adding New Reports
1. Click the "+" button to add a new report
2. Choose a visualization type (table, bar chart, line chart, pie chart, graph, etc.)
3. Write a Cypher query to fetch your data
4. Configure the visualization settings

### Example Queries for Graphiti Data

#### Find entities with most relationships:
```cypher
MATCH (e:Entity)-[r:RELATES_TO]-()
RETURN e.name as Entity, e.summary as Summary, count(r) as Connections
ORDER BY Connections DESC
LIMIT 10
```

#### Find recent episodes and their mentioned entities:
```cypher
MATCH (ep:Episodic)-[m:MENTIONS]->(e:Entity)
WHERE ep.created_at > datetime() - duration('P7D')
RETURN ep.name as Episode, collect(e.name) as MentionedEntities, ep.created_at as Date
ORDER BY Date DESC
```

#### Analyze relationship facts:
```cypher
MATCH ()-[r:RELATES_TO]->()
WHERE r.fact IS NOT NULL
RETURN r.fact as Fact, r.created_at as Created
ORDER BY Created DESC
LIMIT 50
```

#### Find entity clusters:
```cypher
MATCH (e1:Entity)-[:RELATES_TO]-(e2:Entity)-[:RELATES_TO]-(e3:Entity)
WHERE e1 <> e3
RETURN e1.name, e2.name, e3.name
LIMIT 20
```

## Tips for Using NeoDash with Graphiti

1. **Use Parameters**: Create dashboard parameters for group_id to filter data by specific conversation groups
2. **Time-based Filtering**: Use the created_at timestamps to create time-based reports
3. **Graph Visualizations**: The graph reports work great for exploring entity relationships
4. **Export Options**: NeoDash allows you to export reports as images or PDFs
5. **Sharing**: You can share dashboards with others by exporting the JSON configuration

## Troubleshooting

### Connection Issues
- Make sure your Neo4j container is running: `docker-compose ps`
- Verify the connection details match your Docker setup
- Check that port 7687 is accessible

### No Data Showing
- Verify you have data in your Neo4j database
- Check the Cypher queries in the reports
- Make sure the node labels and property names match your data

### Performance
- For large datasets, add LIMIT clauses to your queries
- Use indexes on frequently queried properties
- Consider using parameters to filter data

## Advanced Features

### Real-time Updates
NeoDash can refresh reports automatically. Set refresh intervals in the report settings.

### Custom Styling
You can customize colors, fonts, and layouts for each report.

### Drill-down Reports
Create interactive reports where clicking on elements filters other reports on the page.

For more advanced features, check the [NeoDash documentation](https://neo4j.com/labs/neodash/).
