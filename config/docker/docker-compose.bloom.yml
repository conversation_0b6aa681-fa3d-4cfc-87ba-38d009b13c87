# Docker Compose configuration optimized for Neo4j Bloom with Graphiti
# This extends the base docker-compose.yml with Bloom-specific optimizations

services:
  graph:
    extends:
      file: docker-compose.yml
      service: graph
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - NEO4J_URI=bolt://neo4j:${NEO4J_PORT:-7687}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - PORT=8000

  neo4j:
    image: neo4j:5.26.2-enterprise
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget -qO- http://localhost:${NEO4J_PORT:-7474} || exit 1",
        ]
      interval: 1s
      timeout: 10s
      retries: 10
      start_period: 3s
    ports:
      - "7474:7474" # HTTP (Neo4j Browser)
      - "${NEO4J_PORT:-7687}:${NEO4J_PORT:-7687}" # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
      - ./bloom_perspectives:/var/lib/neo4j/bloom_perspectives:ro
      - ./bloom_config:/var/lib/neo4j/bloom_config:ro
    environment:
      # Authentication
      - NEO4J_AUTH=${NEO4J_USER:-neo4j}/${NEO4J_PASSWORD:-password}
      
      # Enterprise license (required for Bloom)
      - NEO4J_ACCEPT_LICENSE_AGREEMENT=yes
      
      # Memory settings optimized for graph visualization and Bloom
      - NEO4J_server_memory_heap_initial__size=${NEO4J_HEAP_INITIAL_SIZE:-1G}
      - NEO4J_server_memory_heap_max__size=${NEO4J_HEAP_MAX_SIZE:-4G}
      - NEO4J_server_memory_pagecache_size=${NEO4J_PAGECACHE_SIZE:-2G}
      
      # Enable APOC and other useful plugins
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      
      # Security settings for development
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*
      
      # Browser and Bloom configuration
      - NEO4J_browser_remote__content__hostname__verification=false
      - NEO4J_browser_post__connect__cmd=play start
      
      # Bloom-specific settings
      - NEO4J_dbms_bloom_enabled=true
      - NEO4J_dbms_bloom_license__file=/var/lib/neo4j/bloom_config/bloom.license
      
      # Full-text search configuration for better Bloom experience
      - NEO4J_dbms_default__database=neo4j
      - NEO4J_db_index_fulltext_eventually__consistent=false
      
      # Performance tuning for visualization workloads
      - NEO4J_dbms_query_cache_size=1000
      - NEO4J_dbms_query_plan_cache_size=1000
      - NEO4J_cypher_min_replan_interval=10s
      
      # Logging configuration
      - NEO4J_server_logs_user_stdout_enabled=true
      - NEO4J_server_logs_debug_level=INFO
      
      # Network settings
      - NEO4J_server_default__listen__address=0.0.0.0
      - NEO4J_server_bolt_listen__address=0.0.0.0:7687
      - NEO4J_server_http_listen__address=0.0.0.0:7474
      
      # Transaction settings for large graph operations
      - NEO4J_db_transaction_timeout=60s
      - NEO4J_dbms_transaction_concurrent_maximum=1000

  # Optional: Bloom perspective manager service
  bloom-manager:
    build:
      context: .
      dockerfile: Dockerfile.bloom-manager
    depends_on:
      neo4j:
        condition: service_healthy
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=${NEO4J_USER:-neo4j}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-password}
    volumes:
      - ./bloom_perspectives:/app/perspectives:ro
      - ./bloom_config:/app/config
    command: ["python", "manage_perspectives.py"]
    profiles:
      - bloom-tools

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:

# Usage instructions:
#
# 1. For basic Bloom support with community edition:
#    docker-compose -f config/docker/docker-compose.yml up
#
# 2. For full Bloom support with enterprise features:
#    docker-compose -f config/docker/docker-compose.bloom.yml up
#
# 3. To include Bloom management tools:
#    docker-compose -f config/docker/docker-compose.bloom.yml --profile bloom-tools up
#
# Note: Enterprise edition requires a valid Neo4j license for production use.
# For development and evaluation, you can use the enterprise edition for free.
