# Graphiti Configuration Files

This directory contains configuration files for Graphiti and related services.

## Directory Structure

```
config/
└── docker/                # Docker and container configurations
    ├── docker-compose.yml      # Main Docker Compose configuration
    ├── docker-compose.test.yml # Test environment configuration
    └── docker-compose.bloom.yml # Neo4j Bloom integration setup
```

## Docker Configurations

### Main Configuration (`docker/docker-compose.yml`)

The primary Docker Compose configuration for running Graphiti with Neo4j.

**Services included:**
- Neo4j database with APOC plugin
- Configured for Graphiti compatibility

**Usage:**
```bash
# Start services
docker-compose -f config/docker/docker-compose.yml up -d

# Stop services
docker-compose -f config/docker/docker-compose.yml down

# View logs
docker-compose -f config/docker/docker-compose.yml logs -f
```

### Test Configuration (`docker/docker-compose.test.yml`)

Configuration optimized for testing environments.

**Features:**
- Faster startup times
- Test-specific settings
- Isolated test data

**Usage:**
```bash
# Run tests with isolated environment
docker-compose -f config/docker/docker-compose.test.yml up -d
python -m pytest
docker-compose -f config/docker/docker-compose.test.yml down
```

### Bloom Configuration (`docker/docker-compose.bloom.yml`)

Extended configuration that includes Neo4j Bloom for graph visualization.

**Additional services:**
- Neo4j with Bloom plugin enabled
- Enhanced visualization capabilities

**Usage:**
```bash
# Start with Bloom support
docker-compose -f config/docker/docker-compose.bloom.yml up -d

# Access Bloom at http://localhost:7474
```

## Environment Variables

All Docker configurations support these environment variables:

### Neo4j Configuration
```bash
NEO4J_AUTH=neo4j/password          # Database authentication
NEO4J_PLUGINS=["apoc"]             # Required plugins
NEO4J_dbms_security_procedures_unrestricted=apoc.*
```

### Graphiti Configuration
```bash
NEO4J_URI=bolt://localhost:7687     # Database connection
NEO4J_USER=neo4j                   # Database user
NEO4J_PASSWORD=password            # Database password
OPENAI_API_KEY=your_key_here       # Required for LLM functionality
```

## Port Mappings

Default port mappings for all configurations:

- **7474**: Neo4j HTTP interface
- **7687**: Neo4j Bolt protocol
- **7473**: Neo4j HTTPS interface (if enabled)

## Data Persistence

All configurations include volume mounts for data persistence:

```yaml
volumes:
  - neo4j_data:/data
  - neo4j_logs:/logs
  - neo4j_import:/var/lib/neo4j/import
  - neo4j_plugins:/plugins
```

## Configuration Customization

### Override Files

Create `docker-compose.override.yml` in the repository root for local customizations:

```yaml
version: '3.8'
services:
  neo4j:
    environment:
      - NEO4J_AUTH=neo4j/mypassword
    ports:
      - "7475:7474"  # Use different port
```

### Environment Files

Use `.env` files for environment-specific settings:

```bash
# .env
NEO4J_PASSWORD=secure_password
OPENAI_API_KEY=sk-your-key-here
```

## Networking

All services use the default Docker Compose network. Services can communicate using service names:

- Neo4j accessible at `neo4j:7687` from other containers
- HTTP interface at `neo4j:7474`

## Health Checks

Configurations include health checks for reliable startup:

```yaml
healthcheck:
  test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "password", "RETURN 1"]
  interval: 30s
  timeout: 10s
  retries: 5
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Change port mappings if 7474/7687 are in use
2. **Permission Issues**: Ensure Docker has access to volume directories
3. **Memory Issues**: Increase Docker memory limits for large graphs
4. **Plugin Loading**: Verify APOC plugin is properly installed

### Debugging

```bash
# Check service status
docker-compose -f config/docker/docker-compose.yml ps

# View detailed logs
docker-compose -f config/docker/docker-compose.yml logs neo4j

# Execute commands in container
docker-compose -f config/docker/docker-compose.yml exec neo4j cypher-shell
```

### Performance Tuning

For production use, consider:

1. **Memory Settings**: Adjust Neo4j heap and page cache
2. **Storage**: Use SSD storage for better performance
3. **Network**: Configure appropriate network settings
4. **Monitoring**: Add monitoring and alerting

## Security Considerations

1. **Change Default Passwords**: Never use default credentials in production
2. **Network Security**: Restrict access to necessary ports only
3. **SSL/TLS**: Enable HTTPS for production deployments
4. **Backup**: Implement regular backup strategies

## Backup and Recovery

```bash
# Create backup
docker-compose -f config/docker/docker-compose.yml exec neo4j neo4j-admin dump --database=neo4j --to=/backups/backup.dump

# Restore backup
docker-compose -f config/docker/docker-compose.yml exec neo4j neo4j-admin load --from=/backups/backup.dump --database=neo4j --force
```
