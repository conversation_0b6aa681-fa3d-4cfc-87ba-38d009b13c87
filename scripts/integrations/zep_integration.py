import os
import uuid
import json
from zep_cloud.client import Zep
from zep_cloud import Message
import time

# Load API Key from environment variable
API_KEY = os.getenv('ZEP_API_KEY')
if not API_KEY:
    raise ValueError("ZEP_API_KEY environment variable is required")
client = Zep(api_key=API_KEY)

USER_ID = "Myself(7)"
UPLOAD_DELAY = 10  # seconds, go slow to avoid rate limits

def chunked(iterable, n):
    for i in range(0, len(iterable), n):
        yield iterable[i:i + n]

def safe_add_memory(client, session_id, messages, max_retries=5):
    for attempt in range(max_retries):
        try:
            client.memory.add(session_id=session_id, messages=messages)
            return True
        except Exception as e:
            if "429" in str(e) or "Rate limit" in str(e):
                wait_time = 60 * (attempt + 1)  # Exponential backoff: 1min, 2min, ...
                print(f"[RATE LIMIT] Hit rate limit, waiting {wait_time} seconds before retrying...")
                time.sleep(wait_time)
            else:
                raise
    print("[ERROR] Max retries exceeded for memory.add")
    return False

def safe_add_session(client, user_id, session_id, max_retries=5):
    for attempt in range(max_retries):
        try:
            client.memory.add_session(user_id=user_id, session_id=session_id)
            time.sleep(UPLOAD_DELAY)  # Wait after each session creation
            return True
        except Exception as e:
            if "429" in str(e) or "Rate limit" in str(e):
                wait_time = 60 * (attempt + 1)
                print(f"[RATE LIMIT] Hit rate limit on add_session, waiting {wait_time} seconds before retrying...")
                time.sleep(wait_time)
            elif "already exists" in str(e):
                print(f"[DEBUG] Session {session_id} already exists, skipping add.")
                return True
            else:
                raise
    print(f"[ERROR] Max retries exceeded for add_session {session_id}")
    return False

def upload_chats(json_path: str):
    with open(json_path, "r") as f:
        conversations = json.load(f)

    # Debug print: show the first 2 items to inspect structure
    print("[DEBUG] First 2 conversations loaded:")
    for idx, item in enumerate(conversations[:2]):
        print(f"[DEBUG] Conversation {idx+1}: {json.dumps(item, indent=2)[:1000]}")

    MAX_CONTENT_LENGTH = 2500
    for i, convo in enumerate(conversations):
        session_id = f"chat_{i+1}"
        user_id = USER_ID  # Use the same user for all sessions

        print(f"▶ Uploading session: {session_id}")

        # Register user & session (handle user/session already exists error)
        try:
            client.user.add(user_id=user_id, first_name="Imported", last_name="User")
        except Exception as e:
            if "user already exists" in str(e):
                print(f"[DEBUG] User {user_id} already exists, skipping add.")
            else:
                raise

        safe_add_session(client, user_id, session_id)

        # Parse messages from 'mapping'
        messages = []
        mapping = convo.get("mapping", {})
        # Sort mapping by create_time if available, else insertion order
        mapping_items = [
            m for m in mapping.values()
            if isinstance(m, dict) and isinstance(m.get("message"), dict)
        ]
        def get_time(m):
            ct = m.get("message", {}).get("create_time")
            return ct if ct is not None else 0
        mapping_items.sort(key=get_time)
        for m in mapping_items:
            msg = m.get("message", {})
            author = msg.get("author", {})
            role = author.get("role", "")
            if role not in ("user", "assistant"):  # skip system, tool, etc
                continue
            content_parts = msg.get("content", {}).get("parts", [])
            content = content_parts[0] if content_parts else ""
            if not isinstance(content, str):
                print(f"[DEBUG] Skipping non-string content in {session_id}: {type(content)}")
                continue
            timestamp = msg.get("create_time")
            if len(content) > MAX_CONTENT_LENGTH:
                print(f"[DEBUG] Using graph.add for large message in {session_id} (len={len(content)})")
                try:
                    client.graph.add(user_id=user_id, type="text", data=content)
                    print(f"✅ Uploaded large message to graph for {session_id}")
                except Exception as e:
                    print(f"[ERROR] Failed to upload large message to graph for {session_id}: {e}")
                continue
            messages.append(Message(
                role=role,
                content=content,
                role_type=role,  # Zep expects 'user' or 'assistant'
                timestamp=timestamp
            ))

        # Send to Zep
        if messages:
            for batch_num, batch in enumerate(chunked(messages, 30), 1):
                success = safe_add_memory(client, session_id, batch)
                if success:
                    print(f"✅ Uploaded batch {batch_num} ({len(batch)} messages) to {session_id}")
                else:
                    print(f"[ERROR] Failed to upload batch {batch_num} to {session_id} after retries.")
                time.sleep(UPLOAD_DELAY)  # Wait after each upload to avoid hammering the API
            print(f"✅ Uploaded {len(messages)} messages to {session_id}")
        else:
            print(f"⚠️  No messages found in {session_id}")

    client.close()
    print("✅ All done!")

if __name__ == "__main__":
    upload_chats("conversations.json")